import createUrlRegex from 'url-regex';

const keywordRegex = /(?:\b(?:guidance|outlook)\b)/gi;
const emptyTagRegex = /<[^/][^>]*>(\s|(&nbsp;))<\/[^>]*>/g;

// that starts with 'MECS:'
export const buildKeyPhrasesRegex = (allowDollar: boolean, allowPercentages: boolean, allowQuarters: boolean) => {
  const keyPhrasesRegexes: string[] = [];
  const numberRegex = /\d+(?:,\d*)?(?:\.\d*)?/.source;
  if (allowDollar) {
    const amountRegex = `${numberRegex}(?:\\s(?:thousand|million|billion|trillion)s?|[KMBT])?`;
    const formattedDollarRegex = /\d{1,3}(?:,\d{3})+(?:\.\d+)?[KMBT]?/.source;
    const dollarRegex = `\\B(?:\\$[+-]?|[+-]?\\$)(?:${formattedDollarRegex}|${amountRegex})\\b`;
    const dollarRangeRegex = `\\+?\\$${amountRegex}-\\$${amountRegex}`;
    const dollarOutsideParensRegex = `\\$\\(${amountRegex}\\)`;
    const dollarInsideParensRegex = `\\(\\$${amountRegex}\\)`;

    keyPhrasesRegexes.push(dollarRangeRegex, dollarOutsideParensRegex, dollarInsideParensRegex, dollarRegex);
  }

  if (allowPercentages) {
    const percentSignRegex = '%\\B|\\spercent\\b';
    const percentRegex = `[-+]?${numberRegex}(?:${percentSignRegex})`;
    const percentRangeRegex = `${numberRegex}(${percentSignRegex})-${numberRegex}\\1`;
    const missingPercentRangeRegex = `${numberRegex}-${numberRegex}(?:${percentSignRegex})`;
    keyPhrasesRegexes.push(missingPercentRangeRegex, percentRangeRegex, percentRegex);
  }

  if (allowQuarters) {
    const quarterRegex = /(?:\bQ[1234]\b)|(?:\b(?:first|second|third|fourth)[\s-]quarter\b)/.source;
    keyPhrasesRegexes.push(quarterRegex);
  }

  if ((keyPhrasesRegexes?.length ?? 0) === 0) {
    return null;
  }

  const keyPhrasesRegex = (keyPhrasesRegexes || []).map(regex => `(?:${regex})`).join('|');
  return RegExp(keyPhrasesRegex, 'ig');
};

// that starts with 'MECS:'
export const buildBeatsAndMissesRegex = () => {
  const keyPhrasesRegexes: string[] = [];
  const dollarRegex = `(\\$)`;
  keyPhrasesRegexes.push(dollarRegex);

  const percentRegex = `(%)`;
  keyPhrasesRegexes.push(percentRegex);

  const quarterRegex = /(?:\bQ[1234]\b)|(?:\b(?:first|second|third|fourth)[\s-]quarter\b)/.source;
  keyPhrasesRegexes.push(quarterRegex);

  keyPhrasesRegexes.push(`\\b(beat).*?\\b`);
  keyPhrasesRegexes.push(`\\b(miss).*?\\b`);

  const keyPhrasesRegex = (keyPhrasesRegexes || []).map(regex => `(?:${regex})`).join('|');
  return RegExp(keyPhrasesRegex, 'ig');
};

export const urlRegex = createUrlRegex();

export const formatKeyword = (keyword: string, isNegative?: boolean) =>
  `<span class="standout standout--${isNegative ? 'negative' : 'positive'}">${keyword}</span>`;

function findTextNodes(element: HTMLElement) {
  // The third argument is optional and the fourth argument is obsolete but IE11 requires them
  // tslint:disable-next-line:deprecation
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const treeWalker = document.createTreeWalker(element, NodeFilter.SHOW_TEXT, null, false); // doesn't seem to be deprecated yet? (11/30/2018)
  const textNodes: Node[] = [];
  while (treeWalker.nextNode()) {
    textNodes.push(treeWalker.currentNode);
  }
  return textNodes;
}

// highlight keywords
// * dollar amount: $10, $10M, $10.55K, -$15, $-10.55, $10.55-$12.55, $10K-$20K
// * positive, negative percentages: -10%, 10.55%, 10 percent, 55.55 percent, 10%-20%, 10.55 percent - 20.55 percent
// * Q1, Q2, Q3, Q4
// * first quarter - fourth quarter
// * keywords - guidance, outlook, ask, bid
export function highlightKeywords(
  text: string,
  keyPhrasesRegex: RegExp | null,
  keyPhrasesReplace: (text: string) => string,
  wrapLinks = false,
): string {
  const root = document.createElement('div');
  root.innerHTML = text?.replace(emptyTagRegex, '');

  findTextNodes(root).forEach((node: Node) => {
    let didMatch = false;
    let replacedHtml = node.nodeValue || '';

    const replaceHtml = (regex: RegExp, replace: (text: string) => string) => {
      replacedHtml = replacedHtml?.replace(regex, match => {
        didMatch = true; // ugh
        return replace(match);
      });
    };

    // do not wrap links within anchors
    if (typeof replacedHtml === 'string') {
      const parent = node.parentNode;

      if (wrapLinks && parent && parent.nodeType === Node.ELEMENT_NODE) {
        const el: HTMLElement = parent as HTMLElement;
        if (el.tagName.toLowerCase() !== 'a') {
          replaceHtml(urlRegex, url => `<a href="${/^https?:\/\//i.test(url) ? url : `https://${url}`}">${url}</a>`);
        }
      }

      if (didMatch) {
        replacedHtml = highlightKeywords(replacedHtml, keyPhrasesRegex, keyPhrasesReplace);
      } else {
        if (keyPhrasesRegex) {
          replaceHtml(keyPhrasesRegex, keyPhrasesReplace);
        }

        replaceHtml(keywordRegex, keyword => formatKeyword(keyword, false));
      }

      if (didMatch) {
        const replacement = document.createElement('span');
        replacement.innerHTML = replacedHtml;
        if (parent) {
          parent.replaceChild(replacement, node);
        }
      }
    }
  });
  return root.innerHTML;
}
