'use client';
import React, { useCallback, useEffect } from 'react';
import styled, { useTheme } from '@benzinga/themetron';
import { SessionContext } from '@benzinga/session-context';
import { CalendarManager, Dividend } from '@benzinga/calendar-manager';
import Link from 'next/link';
import { PortfolioQuoteTableEdgeCTA } from './PortfolioQuoteTableEdgeCTA';
import { WatchlistTableButton } from './WatchlistTableButton';
import { DelayedQuote } from '@benzinga/quotes-manager';
import { getColorByValue } from '@benzinga/frontend-utils';
import { AddAllWatchlistTableButton } from './AddAllWatchlistTableButton';
import { usePermission } from '@benzinga/user-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { useSearchParams } from 'next/navigation';

export interface LayoutDividendProps {
  bypassPaywall?: boolean;
  paywall_label?: string;
  symbols?: string[];
  quotes?: DelayedQuote[];
  initialDividends?: Dividend[];
}

export const LayoutDividend: React.FC<LayoutDividendProps> = ({
  bypassPaywall = false,
  initialDividends,
  paywall_label = 'Easy Income Portfolio',
  quotes,
  symbols,
}) => {
  const prepareQuotesData = quotes => {
    if (quotes && quotes?.length > 0) {
      const _quotesData = quotes.reduce((a, x) => {
        a[x.symbol] = x;
        return a;
      }, []);
      return _quotesData;
    }
    return [];
  };
  const theme = useTheme();
  const session = React.useContext(SessionContext);
  const calendarManager = session.getManager(CalendarManager);
  const [diviendData, setDiviendData] = React.useState<Dividend[]>(initialDividends ?? []);
  const [dataToDisplay, setDataToDisplay] = React.useState<Dividend[]>(
    initialDividends ? initialDividends?.slice(0, 10) : [],
  );
  const [showMore, setShowMore] = React.useState<boolean>(false);
  const [quotesData, setQuotesData] = React.useState<DelayedQuote[]>(prepareQuotesData(quotes));
  const hasPermission = usePermission('com/read', 'unlimited-calendars');
  const [isPaywallActive, setIsPaywallActive] = React.useState(false);
  const searchParams = useSearchParams();
  const slug = searchParams?.get('slug')?.replaceAll('-', '_');

  useEffect(() => {
    if (!bypassPaywall) {
      if (!hasPermission) {
        setIsPaywallActive(true);
        session.getManager(TrackingManager).trackPaywallEvent('view', {
          paywall_id: 'layout-default-screener-table-benzinga-edge',
          paywall_type: 'soft',
          placement: slug ?? 'screener',
        });
      } else {
        setIsPaywallActive(false);
      }
    }
  }, [hasPermission, bypassPaywall, session, slug]);

  const getQuoteCalendarData = useCallback(
    async symbols => {
      if (symbols.length > 0) {
        const { ok: dividendRepo } = await calendarManager.getCalendarData(
          'dividends',
          {
            pageSize: 1000,
            symbols: symbols,
          },
          true,
        );
        if (dividendRepo && dividendRepo?.length > 0) {
          const processedQuote: string[] = [];
          const dividendData: Dividend[] = [];
          dividendRepo.forEach(item => {
            if (item?.ticker && !processedQuote.includes(item.ticker)) {
              processedQuote.push(item.ticker);
              dividendData.push(item);
            }
          });
          setDiviendData(dividendData);
          setDataToDisplay(dividendData?.slice(0, 10));
        }
      }
    },
    [calendarManager],
  );

  useEffect(() => {
    if (!initialDividends) {
      getQuoteCalendarData(symbols);
    }
  }, [getQuoteCalendarData, session, symbols, initialDividends]);

  return (
    <LayoutDividendContainer>
      <div className="stock-quotes-table-container font-manrope calendar-container relative bg-white">
        <div className="quote-table-wrap">
          <table className="table-auto">
            <thead>
              <tr>
                <th colSpan={1}>
                  <div className="cell-item">Ticker</div>
                </th>
                <th colSpan={1}>
                  <div className="cell-item">Pay/Year</div>
                </th>
                <th colSpan={1}>
                  <div className="cell-item">Last Price</div>
                </th>
                <th colSpan={1}>
                  <div className="cell-item">Dividend</div>
                </th>
                <th colSpan={1}>
                  <div className="cell-item">Yield</div>
                </th>
                <th colSpan={1}>
                  <div className="cell-item">Announcement</div>
                </th>
                <th colSpan={1}>
                  <div className="cell-item">Record</div>
                </th>
                <th colSpan={1}>
                  <div className="cell-item">Ex-Dividend Date</div>
                </th>
                <th colSpan={1}>
                  <div className="cell-item">Payable</div>
                </th>
                <th colSpan={1}>
                  {symbols && symbols.length > 0 && (
                    <div className="cell-item">
                      <AddAllWatchlistTableButton isActive={isPaywallActive} symbols={symbols} />
                    </div>
                  )}
                </th>
              </tr>
            </thead>
            <tbody>
              {dataToDisplay &&
                dataToDisplay?.length > 0 &&
                dataToDisplay.map((quote, i) =>
                  quote ? (
                    <tr className={isPaywallActive ? 'blur-sm' : ''} key={`quote-${i}`}>
                      <td className="ticker-data">
                        <div className="cell-item">
                          <div className="flex info-wrap gap-3">
                            <div className="company-info flex flex-col">
                              <div className="flex items-center gap-2">
                                <Link href={`/quote/${quote.ticker}`}>
                                  <span className="symbol font-semibold">{quote.ticker}</span>{' '}
                                </Link>
                              </div>
                              <div className="industry">{quote.name}</div>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div className="cell-item">{quote.frequency}</div>
                      </td>
                      <td className="font-semibold">
                        <div className="cell-item">
                          <div className="flex flex-col">
                            {quotesData[quote.ticker]?.lastTradePrice
                              ? `$${Number(quotesData[quote.ticker]?.lastTradePrice).toFixed(2)}`
                              : '-'}

                            <div className="flex gap-1">
                              <span
                                className="change"
                                style={{
                                  color: getColorByValue(theme, Number(quotesData[quote.ticker]?.change).toFixed(2)),
                                }}
                                title={Number(quotesData[quote.ticker]?.change).toFixed(2)?.toString()}
                              >
                                ${Number(quotesData[quote.ticker]?.change).toFixed(2) ?? '– '}
                              </span>
                              <span
                                className="change-percent"
                                style={{
                                  backgroundColor: `${getColorByValue(theme, Number(quotesData[quote.ticker]?.changePercent).toFixed(2))}20`,
                                  color: getColorByValue(
                                    theme,
                                    Number(quotesData[quote.ticker]?.changePercent).toFixed(2),
                                  ),
                                }}
                                title={Number(quotesData[quote.ticker]?.changePercent).toFixed(2)?.toString()}
                              >
                                {Number(quotesData[quote.ticker]?.changePercent).toFixed(2) ?? '– '}%
                              </span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="price font-semibold">
                        <div className="cell-item">${Number(quote?.dividend).toFixed(2)}</div>
                      </td>
                      <td className="font-semibold">
                        <div className="cell-item">
                          {quote?.dividend_yield ? (Number(quote.dividend_yield) * 100).toFixed(2) : '-'}%
                        </div>
                      </td>
                      <td className="font-semibold">
                        <div className="cell-item">{quote?.date ? quote.date : '-'}</div>
                      </td>
                      <td className="font-semibold">
                        <div className="cell-item">{quote?.record_date ? quote.record_date : '-'}</div>
                      </td>
                      <td className="font-semibold">
                        <div className="cell-item">{quote?.ex_dividend_date ? quote.ex_dividend_date : '-'}</div>
                      </td>
                      <td className="font-semibold">
                        <div className="cell-item">{quote?.payable_date ? quote.payable_date : '-'}</div>
                      </td>
                      <td>
                        <div className="cell-item">
                          <WatchlistTableButton isActive={isPaywallActive} symbol={quote.ticker} />
                        </div>
                      </td>
                    </tr>
                  ) : null,
                )}
            </tbody>
          </table>
        </div>
        {!isPaywallActive && (
          <div className="show-more center">
            <div
              onClick={() => {
                if (!showMore) {
                  setDataToDisplay(diviendData);
                } else {
                  setDataToDisplay(diviendData?.slice(0, 10));
                }
                setShowMore(!showMore);
              }}
            >
              Show {showMore ? 'Less' : 'More'}
            </div>
          </div>
        )}
        {isPaywallActive && (
          <div className="absolute top-1/2 w-full -translate-y-2/4">
            <PortfolioQuoteTableEdgeCTA
              t_code={'be34be9dimibe3bz84'}
              title={paywall_label ? paywall_label : 'Easy Income Portfolio'}
              utm={'PaywallEasyIncome'}
            />
          </div>
        )}
      </div>
      <div className="bz-pro-action pt-2">
        <a href="https://www.benzinga.com/go/benzinga-pro" rel="noreferrer" target="_blank">
          Start Your Free 14-Day Benzinga Pro Trial
        </a>
      </div>
    </LayoutDividendContainer>
  );
};

const LayoutDividendContainer = styled.div`
  .stock-quotes-table-container {
    width: 100%;
    display: block;
    overflow-x: auto;
    clear: both;
    border: 1px solid #e1ebfa;
    border-radius: 4px;
    .quote-table-wrap {
      max-height: 800px;
      min-height: 600px;
      overflow: auto;
    }

    .show-more {
      border-top: 1px solid #e1ebfa;
      padding: 10px;
    }

    table {
      width: 100%;
      margin-bottom: 0.2rem;
      table-layout: auto;
      display: table;
      height: 100%;
      .cell-item {
        border-left: 1px solid #e1ebfa;
        padding-left: 12px;
      }
    }

    thead {
      border-bottom: 1px solid #e1ebfa;
      background: #f2f8ff;

      tr {
        text-align: left;
        th {
          color: #5b7292;
          padding: 12px 8px 12px 0px;
          min-width: 120px;
          &:first-of-type {
            width: 190px;
            .cell-item {
              border: none;
            }
          }

          &:nth-child(2),
          &:nth-child(4),
          &:nth-child(5) {
            min-width: 40px;
          }
          &:nth-child(8) {
            min-width: 160px;
          }
          &:last-of-type {
            min-width: 100px;
          }
        }
      }
    }

    tbody {
      overflow: auto;

      tr {
        border-collapse: collapse;
        box-sizing: inherit;
        border-bottom: 1px solid #e1ebfa;
        width: 100%;

        td {
          padding: 6px 10px 6px 0;
          color: ${({ theme }) => theme.colorPalette.black};
          &:first-of-type {
            width: 190px;
            .cell-item {
              border: none;
            }
          }
          &:nth-child(2),
          &:nth-child(4),
          &:nth-child(5) {
            min-width: 40px;
          }
          &:last-of-type {
            min-width: 100px;
          }
          &.ticker-data {
            img {
              width: 35px;
            }
            .company-info {
              a {
                color: #000000;
              }
              .company-name {
                padding: 2px 4px;
                border-radius: 4px;
                background: #e1ebfa;
                font-size: 12px;
                line-height: 16px;
              }
              .industry {
                font-size: 0.8rem;
                color: #395173;
              }
            }
          }
          &.price {
            .change-percent {
              padding: 0px 4px;
              border-radius: 4px;
            }
          }
          &.button-wrap {
            width: 120px;
          }
        }
        &:last-of-type {
          border: none;
        }
      }
    }
  }
`;
