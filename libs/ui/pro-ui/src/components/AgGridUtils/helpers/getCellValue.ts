import { isNilOrEmpty, formatLarge } from '@benzinga/utils';
import { get } from 'lodash';
import { NO_VALUE } from '../../Ticker';
import { ValueFormatterParams } from '@ag-grid-community/core';

interface CellParams {
  value?: string | number;
  data: ValueFormatterParams['data'];
  colDef: {
    field?: string;
    colId?: string;
  };
}

export const getCellValue = <T extends CellParams>(params: T) => {
  //for nested objects like a.b
  const fieldPath = params.colDef.field ?? params.colDef.colId ?? '';
  const value =
    typeof fieldPath === 'string' && fieldPath.includes('.')
      ? params.value !== get(params.data, fieldPath)
        ? get(params.data, fieldPath)
        : params.value
      : params.value;
  if (isNilOrEmpty(value)) {
    return NO_VALUE;
  }
  if (typeof value === 'number') {
    return formatLarge(value);
  }
  if (typeof value === 'string') {
    const parsedValue = parseFloat(value);
    return isNaN(parsedValue) ? NO_VALUE : formatLarge(parsedValue);
  }
  return value;
};
